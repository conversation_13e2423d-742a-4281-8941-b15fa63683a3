#!/bin/bash

# Local Development Script - Runs PocketBase and Vite without Docker

set -e

echo "🚀 Starting StageWise Local Development..."
echo ""

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21+ first."
    exit 1
fi

# Check if Bun is installed
if ! command -v bun &> /dev/null; then
    echo "❌ Bun is not installed. Please install Bun first."
    echo "📖 Install from: https://bun.sh"
    exit 1
fi

# Check if backend is built
if [ ! -f "backend/server" ]; then
    echo "🔧 Building backend..."
    cd backend
    go build -o server ./cmd/server
    cd ..
fi

# Create .env if it doesn't exist
if [ ! -f .env ]; then
    echo "⚙️  Creating .env file..."
    cp .env.example .env
fi

# Set up environment variables
ENV_FILE_LOADED=false
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
PROJECT_ROOT_DIR=$(cd "$SCRIPT_DIR/.." && pwd)

if [ -f "$PROJECT_ROOT_DIR/.env.local" ]; then
  echo "🔧 Loading environment variables from $PROJECT_ROOT_DIR/.env.local"
  set -o allexport; source "$PROJECT_ROOT_DIR/.env.local"; set +o allexport
  ENV_FILE_LOADED=true
elif [ -f "$PROJECT_ROOT_DIR/.env" ]; then
  echo "🔧 Loading environment variables from $PROJECT_ROOT_DIR/.env"
  set -o allexport; source "$PROJECT_ROOT_DIR/.env"; set +o allexport
  ENV_FILE_LOADED=true
fi

if [ "$ENV_FILE_LOADED" = true ]; then
  # Ensure required variables are set (and exported), provide defaults if not in .env file
  export POCKETBASE_DATA_DIR="${POCKETBASE_DATA_DIR:-backend/backend/pb_data}" # Relative to project root
  export VITE_POCKETBASE_URL="${VITE_POCKETBASE_URL:-http://127.0.0.1:8090}"
else
  echo "⚠️ No .env file found in project root ($PROJECT_ROOT_DIR). Using hardcoded default environment variables."
  echo "   Consider creating '$PROJECT_ROOT_DIR/.env.local' or '$PROJECT_ROOT_DIR/.env'."
  export POCKETBASE_DATA_DIR="backend/backend/pb_data" # Relative to project root
  export VITE_POCKETBASE_URL="http://127.0.0.1:8090"
fi

# Resolve POCKETBASE_DATA_DIR to an absolute path
# Assumes POCKETBASE_DATA_DIR is either absolute or relative to the project root
if [[ "$POCKETBASE_DATA_DIR" == /* ]]; then # Absolute path
  export POCKETBASE_DATA_DIR_ABS="$POCKETBASE_DATA_DIR"
else # Relative path (treat as relative to project root)
  export POCKETBASE_DATA_DIR_ABS="$(cd "$PROJECT_ROOT_DIR/$POCKETBASE_DATA_DIR" && pwd)"
fi

echo "📊 Environment:"
echo "  Backend Data: $POCKETBASE_DATA_DIR_ABS (resolved from $POCKETBASE_DATA_DIR)"
echo "  Backend URL:  $VITE_POCKETBASE_URL"
echo "  Frontend URL: http://localhost:5173"
echo ""

# Check if admin user exists, create if needed
if [ ! -d "$POCKETBASE_DATA_DIR_ABS" ] || [ -z "$(find "$POCKETBASE_DATA_DIR_ABS" -name '*.db' -print -quit 2>/dev/null)" ]; then
    echo "🔐 Creating admin user..."
    
    # Get admin credentials from environment or use defaults
    ADMIN_EMAIL="${POCKETBASE_ADMIN_EMAIL:-<EMAIL>}"
    ADMIN_PASSWORD="${POCKETBASE_ADMIN_PASSWORD:-admin123456}"
    
    echo "  Email: $ADMIN_EMAIL"
    echo "  Password: $ADMIN_PASSWORD"
    echo ""
    
    cd backend
    if POCKETBASE_DATA_DIR="$POCKETBASE_DATA_DIR_ABS" POCKETBASE_ADMIN_EMAIL="$ADMIN_EMAIL" POCKETBASE_ADMIN_PASSWORD="$ADMIN_PASSWORD" ./server -dev --dir "$POCKETBASE_DATA_DIR_ABS" superuser create "$ADMIN_EMAIL" "$ADMIN_PASSWORD"; then
        echo "✅ Admin user created!"
    else
        echo "❌ Failed to create admin user. Please check for errors above or in PocketBase logs."
        echo "   Ensure $POCKETBASE_DATA_DIR_ABS is writable and not corrupted."
        # Optionally, exit here if admin creation is critical: exit 1
    fi
    cd ..
    
    echo ""
fi

echo "🎯 Starting development servers..."
echo ""
echo "📋 Available commands in separate terminals:"
echo "  bun run backend:seed       # Seed test data"
echo "  bun run types:generate     # Generate TypeScript types"
echo ""

# Start both processes with concurrently
bun run dev:local