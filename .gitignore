# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Bun
bun.lockb

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# PocketBase data directories
pb_data/
backend/pb_data/
**/pb_data/

# Environment files
.env
.env.*.local

# Keep .env.local as template (not secrets)
# .env.local

# Go build artifacts
backend/tmp/
*.exe
*.exe~
.claude
.bun-cache
.env.example
.env.production
.env.staging