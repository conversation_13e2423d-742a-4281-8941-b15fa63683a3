package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"go/format"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"text/template"
	"unicode"
)

// PocketBaseCollection represents a single collection from the schema.json
type PocketBaseCollection struct {
	ID         string            `json:"id"`
	Name       string            `json:"name"`
	Type       string            `json:"type"` // "base" or "view"
	System     bool              `json:"system"`
	ListRule   *string           `json:"listRule"`
	ViewRule   *string           `json:"viewRule"`
	CreateRule *string           `json:"createRule"`
	UpdateRule *string           `json:"updateRule"`
	DeleteRule *string           `json:"deleteRule"`
	Schema     []PocketBaseField `json:"fields"`    // PocketBase uses "fields"
	Indexes    []string          `json:"indexes"`   // Array of "CREATE INDEX..." strings
	ViewQuery  string            `json:"viewQuery"` // Specific to view collections
	Options    map[string]any    `json:"options"`   // For any other collection options
}

// PocketBaseField represents a single field within a collection's schema.
type PocketBaseField struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Type        string         `json:"type"`
	System      bool           `json:"system"`
	Required    bool           `json:"required"`
	Presentable bool           `json:"presentable"`
	Options     map[string]any `json:"options"` // Fallback for options not directly mapped

	// Directly mapped options from JSON (PocketBase >= 0.20 style)
	Min           interface{} `json:"min"`           // For text (length), number (value), date (value)
	Max           interface{} `json:"max"`           // For text (length), number (value), date (value)
	Pattern       string      `json:"pattern"`       // For text
	NoDecimal     bool        `json:"noDecimal"`     // For number
	Values        []string    `json:"values"`        // For select
	MaxSelect     *int        `json:"maxSelect"`     // For select, relation, file
	MinSelect     *int        `json:"minSelect"`     // For relation
	CollectionID  string      `json:"collectionId"`  // For relation
	CascadeDelete bool        `json:"cascadeDelete"` // For relation
	MaxSize       *int64      `json:"maxSize"`       // For file, editor (bytes)
	MimeTypes     []string    `json:"mimeTypes"`     // For file
	Thumbs        []string    `json:"thumbs"`        // For file
	Protected     bool        `json:"protected"`     // For file
	DisplayFields []string    `json:"displayFields"` // For relation (used by UI, not directly in collection definition)
	NotUnique     bool        `json:"notUnique"`     // For relation with unique constraint
	Query         string      `json:"query"`         // For relation with custom query filter
	Secret        bool        `json:"secret"`        // For json field type
	ConvertUrls   bool        `json:"convertUrls"`   // For editor field type
	ExceptDomains []string    `json:"exceptDomains"` // For email/url
	OnlyDomains   []string    `json:"onlyDomains"`   // For email/url
	OnCreate      bool        `json:"onCreate"`      // For autodate
	OnUpdate      bool        `json:"onUpdate"`      // For autodate
}

// --- Template Data Structures ---
type TemplateData struct {
	PackageName         string
	Imports             []string
	CollectionFunctions []CollectionFunctionData
	CollectionIDToName  map[string]string
}

type CollectionFunctionData struct {
	CollectionNameCamelCase string
	OriginalCollectionName  string
	IsViewCollection        bool
	ViewQuery               string
	Fields                  []string // Stores generated field addition lines
	RelationChecks          []RelationCheckData
	Indexes                 []string // Stores generated AddIndex lines or TODOs
	ListRule                string
	ViewRule                string
	CreateRule              string
	UpdateRule              string
	DeleteRule              string
}

type RelationCheckData struct {
	VariableName   string // e.g., clientsCollection
	CollectionName string // e.g., "clients"
}

const goTemplate = `package {{.PackageName}}

import (
	{{- range .Imports}}
	"{{.}}"
	{{- end}}
)
{{$idToNameMap := .CollectionIDToName}}
{{range .CollectionFunctions}}
{{$currentCollectionName := .OriginalCollectionName}}
// Create{{.CollectionNameCamelCase}}Collection creates the '{{.OriginalCollectionName}}' {{if .IsViewCollection}}view {{end}}collection if it does not exist.
func Create{{.CollectionNameCamelCase}}Collection(app core.App) error {
	{{if .IsViewCollection}}collection := core.NewViewCollection("{{.OriginalCollectionName}}")
	collection.ViewQuery = ` + "`{{ .ViewQuery }}`" + `
	{{- else}}
	collection := core.NewBaseCollection("{{.OriginalCollectionName}}")
	{{- end}}
	{{range .RelationChecks}}
	{{.VariableName}}, err := app.FindCollectionByNameOrId("{{.CollectionName}}")
	if err != nil {
		return fmt.Errorf("dependency collection '{{.CollectionName}}' not found for '{{$currentCollectionName}}': %w", err)
	}
	{{- end}}
	{{range .Fields}}
	{{.}}
	{{- end}}
	{{range .Indexes}}
	{{.}}
	{{- end}}
	collection.ListRule = {{.ListRule}}
	collection.ViewRule = {{.ViewRule}}
	{{if .IsViewCollection}}collection.CreateRule = nil
	collection.UpdateRule = nil
	collection.DeleteRule = nil
	{{- else}}
	collection.CreateRule = {{.CreateRule}}
	collection.UpdateRule = {{.UpdateRule}}
	collection.DeleteRule = {{.DeleteRule}}
	{{- end}}

	// It's good practice to check if the collection already exists before trying to save.
	// However, app.Save(collection) handles this by updating if it exists or creating if it doesn't.
	// For views, it might re-apply the view query.
	// If a more explicit "create only if not exists" is needed, one would app.Dao().FindCollectionByNameOrId first.
	return app.Save(collection)
}
{{end}}

// CreateAllGeneratedCollections iterates over all generated collection creation functions and executes them.
// It's designed to be called once during application initialization.
func CreateAllGeneratedCollections(app core.App) error {
	var multiErr error
	collectionCreationOrder := []func(core.App) error{
		{{- range .CollectionFunctions}}
		Create{{.CollectionNameCamelCase}}Collection,
		{{- end}}
	}

	for _, createFunc := range collectionCreationOrder {
		// It's important to handle dependencies. The current generator sorts collections alphabetically,
		// which might not be the correct order for creation if there are dependencies (e.g., a 'relation' field).
		// The relation checks inside each function help, but a topological sort based on dependencies
		// for the order of calls here would be more robust if strict creation order is critical.
		// For now, we rely on the individual functions' relation checks and PocketBase's ability to handle some out-of-order saves.
		if err := createFunc(app); err != nil {
			// This will return on the first error. If you want to try all and collect errors,
			// you'd need a more complex error handling mechanism (e.g., using a list of errors).
			// For simplicity, we return on the first failure.
			// The error from createFunc should be descriptive enough.
			if multiErr == nil {
				multiErr = err // Capture the first error
			} else {
				// If you want to collect all errors:
				// multiErr = fmt.Errorf("%v; %w", multiErr, err)
			}
			// For now, let's log and continue, or return first error
			// log.Printf("Error creating collection: %v", err) // Requires "log" import
		}
	}
	return multiErr // Return the first error encountered, or nil if all succeed
}
`

var (
	// Regex to parse "CREATE [UNIQUE] INDEX `idx_name` ON `table_name` (col1, `col2`)"
	indexRegex = regexp.MustCompile(`^CREATE (UNIQUE )?INDEX \x60([^\x60]+)\x60 ON \x60([^\x60]+)\x60 \((.+)\)$`)
	// Regex to extract columns from the columns part of the index string
	indexColRegex = regexp.MustCompile(`\x60?([^\x60,\s]+)\x60?`)
)

func main() {
	inputFile := flag.String("inputFile", "schema.json", "Path to the PocketBase schema JSON file")
	outputFile := flag.String("outputFile", "internal/generated_pocketbase_models.go", "Path for the generated Go file")
	packageName := flag.String("packageName", "internal", "The package name for the generated Go file")
	flag.Parse()

	log.Printf("Reading schema from: %s", *inputFile)
	jsonData, err := os.ReadFile(*inputFile)
	if err != nil {
		log.Fatalf("Failed to read schema file: %v", err)
	}

	var collections []PocketBaseCollection
	if err := json.Unmarshal(jsonData, &collections); err != nil {
		log.Fatalf("Failed to unmarshal schema JSON: %v", err)
	}
	log.Printf("Successfully parsed %d collections.", len(collections))

	collectionIdToNameMap := make(map[string]string)
	for _, coll := range collections {
		collectionIdToNameMap[coll.ID] = coll.Name
	}

	templateData := TemplateData{
		PackageName: *packageName,
		Imports: []string{
			"fmt",
			"github.com/pocketbase/pocketbase/core",
			"github.com/pocketbase/pocketbase/tools/types",
		},
		CollectionIDToName: collectionIdToNameMap,
	}

	// Build dependency graph for topological sorting
	dependencyGraph := buildDependencyGraph(collections, collectionIdToNameMap)
	sortedCollections := topologicalSort(collections, dependencyGraph)

	for _, coll := range sortedCollections {
		if coll.System && coll.Type != "view" { // Don't generate for system collections unless they are views defining their schema
			log.Printf("Skipping system collection: %s", coll.Name)
			continue
		}

		log.Printf("Processing collection: %s", coll.Name)

		cfd := CollectionFunctionData{
			CollectionNameCamelCase: toCamelCase(coll.Name),
			OriginalCollectionName:  coll.Name,
			IsViewCollection:        coll.Type == "view",
			ViewQuery:               coll.ViewQuery,
			ListRule:                renderRule(coll.ListRule),
			ViewRule:                renderRule(coll.ViewRule),
			CreateRule:              renderRule(coll.CreateRule),
			UpdateRule:              renderRule(coll.UpdateRule),
			DeleteRule:              renderRule(coll.DeleteRule),
		}

		// Sort fields by their original ID for some stability, or name.
		// PocketBase schema JSON might not guarantee order.
		sort.Slice(coll.Schema, func(i, j int) bool {
			return coll.Schema[i].ID < coll.Schema[j].ID
		})

		for _, field := range coll.Schema {
			// For base collections, system fields (id, created, updated) are auto-managed.
			// For view collections, all fields listed in schema.json (including an 'id' if defined by the view) should be generated.
			if field.System && !cfd.IsViewCollection && (field.Name == "id" || field.Name == "created" || field.Name == "updated") {
				continue
			}

			fieldStr, relationCheck := generateFieldCode(field, collectionIdToNameMap)
			if fieldStr != "" {
				cfd.Fields = append(cfd.Fields, fieldStr)
			}
			if relationCheck != nil {
				// Avoid duplicate relation checks
				exists := false
				for _, rc := range cfd.RelationChecks {
					if rc.VariableName == relationCheck.VariableName {
						exists = true
						break
					}
				}
				if !exists {
					cfd.RelationChecks = append(cfd.RelationChecks, *relationCheck)
				}
			}
		}

		for _, indexSQL := range coll.Indexes {
			cfd.Indexes = append(cfd.Indexes, parseIndexSQL(indexSQL, coll.Name))
		}

		templateData.CollectionFunctions = append(templateData.CollectionFunctions, cfd)
	}

	tmpl, err := template.New("pocketbase_models").Parse(goTemplate)
	if err != nil {
		log.Fatalf("Failed to parse Go template: %v", err)
	}

	var generatedCode bytes.Buffer
	if err := tmpl.Execute(&generatedCode, templateData); err != nil {
		log.Fatalf("Failed to execute template: %v", err)
	}

	formattedCode, err := format.Source(generatedCode.Bytes())
	if err != nil {
		log.Printf("Failed to format generated Go code: %v. Writing unformatted code.", err)
		formattedCode = generatedCode.Bytes() // Write unformatted if formatting fails
	}

	log.Printf("Writing generated code to: %s", *outputFile)
	if err := os.MkdirAll(filepath.Dir(*outputFile), os.ModePerm); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}
	if err := os.WriteFile(*outputFile, formattedCode, 0644); err != nil {
		log.Fatalf("Failed to write generated Go file: %v", err)
	}

	log.Println("Successfully generated PocketBase models Go file.")
}

func toCamelCase(s string) string {
	parts := strings.Split(s, "_")
	for i, part := range parts {
		if part == "" {
			continue
		}
		runes := []rune(part)
		runes[0] = unicode.ToUpper(runes[0])
		parts[i] = string(runes)
	}
	return strings.Join(parts, "")
}

func renderRule(rule *string) string {
	if rule == nil || *rule == "" {
		return `types.Pointer("")` // Default to empty string for rules means public/no rule
	}
	return fmt.Sprintf(`types.Pointer("%s")`, strings.ReplaceAll(*rule, `"`, `\"`))
}

func generateFieldCode(field PocketBaseField, collectionIdToNameMap map[string]string) (string, *RelationCheckData) {
	var sb strings.Builder
	var relationCheck *RelationCheckData

	sb.WriteString(fmt.Sprintf(`collection.Fields.Add(&core.%sField{`, toCamelCase(field.Type)))
	sb.WriteString(fmt.Sprintf("\n\tName:     \"%s\",", field.Name))

	// Autodate fields (created, updated) are managed by PocketBase and do not support 'Required' attribute directly in schema options.
	// Their presence is handled by the system.
	if field.Type != "autodate" {
		if field.Required {
			sb.WriteString("\n\tRequired: true,")
		} else {
			sb.WriteString("\n\tRequired: false,") // Explicitly false for clarity
		}
	}

	switch field.Type {
	case "text", "json": // json type often uses text-like options
		if max, ok := field.Max.(float64); ok { // JSON numbers are float64
			sb.WriteString(fmt.Sprintf("\n\tMax:      %d,", int(max)))
		}
		if min, ok := field.Min.(float64); ok {
			sb.WriteString(fmt.Sprintf("\n\tMin:      %d,", int(min)))
		}
		if field.Pattern != "" {
			sb.WriteString(fmt.Sprintf("\n\tPattern:  `%s`,", field.Pattern))
		}
		if field.Type == "json" && field.Secret {
			sb.WriteString("\n\tSecret:   true,")
		}

	case "number":
		if field.Min != nil {
			sb.WriteString(fmt.Sprintf("\n\tMin:      types.Pointer(%.1f),", field.Min.(float64)))
		}
		if field.Max != nil {
			sb.WriteString(fmt.Sprintf("\n\tMax:      types.Pointer(%.1f),", field.Max.(float64)))
		}
		if field.NoDecimal {
			sb.WriteString("\n\tNoDecimal: true,")
		}
	case "select":
		if field.MaxSelect != nil {
			sb.WriteString(fmt.Sprintf("\n\tMaxSelect: %d,", *field.MaxSelect))
		} else {
			sb.WriteString("\n\tMaxSelect: 1,") // Default for select if not specified often means single select (0 or 1)
		}
		if len(field.Values) > 0 {
			sb.WriteString("\n\tValues:   []string{")
			for i, val := range field.Values {
				sb.WriteString(fmt.Sprintf("\"%s\"", val))
				if i < len(field.Values)-1 {
					sb.WriteString(", ")
				}
			}
			sb.WriteString("},")
		}
	case "relation":
		targetCollectionName, ok := collectionIdToNameMap[field.CollectionID]
		if !ok {
			log.Printf("Warning: Could not find collection name for ID: %s (field: %s). Using ID directly.", field.CollectionID, field.Name)
			targetCollectionName = field.CollectionID // Fallback, but app.FindCollectionByNameOrId needs a name primarily
		}

		variableName := strings.ToLower(toCamelCase(targetCollectionName)) + "Collection"
		relationCheck = &RelationCheckData{
			VariableName:   variableName,
			CollectionName: targetCollectionName,
		}
		sb.WriteString(fmt.Sprintf("\n\tCollectionId:  %s.Id,", variableName))

		if field.MaxSelect != nil {
			sb.WriteString(fmt.Sprintf("\n\tMaxSelect:     %d,", *field.MaxSelect))
		} else {
			sb.WriteString("\n\tMaxSelect:     1,") // Default for relation usually 1 (single)
		}
		if field.MinSelect != nil && *field.MinSelect > 0 { // Only add if MinSelect is specified and > 0
			sb.WriteString(fmt.Sprintf("\n\tMinSelect:     types.Pointer(%d),", *field.MinSelect))
		}

		if field.CascadeDelete {
			sb.WriteString("\n\tCascadeDelete: true,")
		} else {
			sb.WriteString("\n\tCascadeDelete: false,")
		}
		if len(field.DisplayFields) > 0 {
			// DisplayFields is for UI, not directly part of core.RelationField struct used in app.Save()
			// We can add it as a comment if needed.
			sb.WriteString(fmt.Sprintf("\n\t// DisplayFields: %v (Note: For UI, not directly set in schema here)", field.DisplayFields))
		}

	case "file":
		if field.MaxSelect != nil {
			sb.WriteString(fmt.Sprintf("\n\tMaxSelect: %d,", *field.MaxSelect))
		} else {
			sb.WriteString("\n\tMaxSelect: 1,") // Default for file often 1 if not specified
		}
		if field.MaxSize != nil {
			sb.WriteString(fmt.Sprintf("\n\tMaxSize:   %d,", *field.MaxSize))
		}
		if len(field.MimeTypes) > 0 {
			sb.WriteString("\n\tMimeTypes: []string{")
			for i, mt := range field.MimeTypes {
				sb.WriteString(fmt.Sprintf("\"%s\"", mt))
				if i < len(field.MimeTypes)-1 {
					sb.WriteString(", ")
				}
			}
			sb.WriteString("},")
		}
		if len(field.Thumbs) > 0 {
			sb.WriteString("\n\tThumbs:    []string{")
			for i, th := range field.Thumbs {
				sb.WriteString(fmt.Sprintf("\"%s\"", th))
				if i < len(field.Thumbs)-1 {
					sb.WriteString(", ")
				}
			}
			sb.WriteString("},")
		}
		if field.Protected {
			sb.WriteString("\n\tProtected: true,")
		}

	case "editor":
		if field.ConvertUrls {
			sb.WriteString("\n\tConvertUrls: true,")
		}
		// MaxSize might apply to editor content length in some contexts
		if field.MaxSize != nil && *field.MaxSize > 0 {
			sb.WriteString(fmt.Sprintf("\n\t// MaxSize: %d (Note: For editor content if applicable)", *field.MaxSize))
		}

	case "email", "url":
		if len(field.ExceptDomains) > 0 {
			sb.WriteString("\n\tExceptDomains: []string{")
			for i, domain := range field.ExceptDomains {
				sb.WriteString(fmt.Sprintf("\"%s\"", domain))
				if i < len(field.ExceptDomains)-1 {
					sb.WriteString(", ")
				}
			}
			sb.WriteString("},")
		}
		if len(field.OnlyDomains) > 0 {
			sb.WriteString("\n\tOnlyDomains: []string{")
			for i, domain := range field.OnlyDomains {
				sb.WriteString(fmt.Sprintf("\"%s\"", domain))
				if i < len(field.OnlyDomains)-1 {
					sb.WriteString(", ")
				}
			}
			sb.WriteString("},")
		}
		// Date, Bool have no special options other than Required handled above
		// 'user' type field is essentially a relation to the users collection.
		// 'autodate' type fields (created, updated) are usually handled by PocketBase itself for base collections.

	case "autodate":
		if field.OnCreate {
			sb.WriteString("\n\tOnCreate: true,")
		}
		if field.OnUpdate {
			sb.WriteString("\n\tOnUpdate: true,")
		}
		// If neither is true, PocketBase will error, this should be caught by schema validation ideally
		// or the schema should guarantee one is true.
	}

	sb.WriteString("\n})")
	return sb.String(), relationCheck
}

func parseIndexSQL(sql string, collectionName string) string {
	matches := indexRegex.FindStringSubmatch(sql)
	if len(matches) == 5 {
		isUnique := matches[1] == "UNIQUE "
		indexName := matches[2]
		// matches[3] is the table name, we assume it matches current collection context
		columnsPart := matches[4]

		colMatches := indexColRegex.FindAllStringSubmatch(columnsPart, -1)
		var columns []string
		for _, cm := range colMatches {
			if len(cm) > 1 {
				columns = append(columns, fmt.Sprintf("\"%s\"", cm[1]))
			}
		}

		if len(columns) > 0 {
			// Simple index, assuming no WHERE clause for now in this auto-generation
			return fmt.Sprintf("collection.AddIndex(\"%s\", %t, %s, \"\")", indexName, isUnique, strings.Join(columns, ", "))
		}
	}
	// Fallback for complex or unparsable indexes
	return fmt.Sprintf("// TODO: Manually verify or add index: %s", sql)
}

// buildDependencyGraph creates a dependency graph based on relation fields
func buildDependencyGraph(collections []PocketBaseCollection, idToNameMap map[string]string) map[string][]string {
	// Map of collection name to its dependencies
	dependencies := make(map[string][]string)

	// Initialize with empty dependencies
	for _, coll := range collections {
		dependencies[coll.Name] = []string{}
	}

	// Build dependencies based on relation fields
	for _, coll := range collections {
		for _, field := range coll.Schema {
			if field.Type == "relation" && field.CollectionID != "" {
				if targetName, exists := idToNameMap[field.CollectionID]; exists {
					// Add targetName as a dependency for this collection
					dependencies[coll.Name] = append(dependencies[coll.Name], targetName)
				}
			}
		}
	}

	return dependencies
}

// topologicalSort orders collections so that dependencies come before dependent collections
func topologicalSort(collections []PocketBaseCollection, dependencyGraph map[string][]string) []PocketBaseCollection {
	// Map for faster lookup of collections by name
	collMap := make(map[string]PocketBaseCollection)
	for _, coll := range collections {
		collMap[coll.Name] = coll
	}

	// Track visited and temporarily visited nodes for cycle detection
	visited := make(map[string]bool)
	tempVisited := make(map[string]bool)

	// Result array in topologically sorted order
	var result []PocketBaseCollection

	// Visit function for DFS
	var visit func(string) bool
	visit = func(name string) bool {
		// Check if already visited (fully processed)
		if visited[name] {
			return true
		}

		// Check for cycles
		if tempVisited[name] {
			log.Printf("Warning: Circular dependency detected involving collection '%s'", name)
			return false
		}

		// Mark as temporarily visited for cycle detection
		tempVisited[name] = true

		// Visit all dependencies first
		for _, dep := range dependencyGraph[name] {
			visit(dep)
		}

		// Mark as fully visited
		delete(tempVisited, name)
		visited[name] = true

		// Add to result if it exists (some dependencies might be to system collections we don't generate)
		if coll, exists := collMap[name]; exists {
			result = append(result, coll)
		}

		return true
	}

	// Process all collections
	for _, coll := range collections {
		if !visited[coll.Name] {
			visit(coll.Name)
		}
	}

	// Collections should now be ordered with dependencies first
	return result
}
