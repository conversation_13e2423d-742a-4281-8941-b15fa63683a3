package main

import (
	"fmt"
	"log"
	"math/rand"
	"os"
	"stagewise-backend/internal"
	"strings"
	"time"

	"github.com/pocketbase/pocketbase"
	"github.com/pocketbase/pocketbase/core"
)

func Start(pb *pocketbase.PocketBase) {
	go func() {
		// Set the current working directory to the PocketBase data directory
		// This ensures PocketBase creates its files in the right location
		if err := os.Chdir(pb.DataDir()); err != nil {
			log.Printf("Warning: Could not change directory to %s: %v", pb.DataDir(), err)
		} else {
			log.Printf("Changed directory to %s", pb.DataDir())
		}

		adminEmail := os.Getenv("POCKETBASE_ADMIN_EMAIL")
		adminPassword := os.Getenv("POCKETBASE_ADMIN_PASSWORD")
		if adminEmail == "" {
			adminEmail = internal.AppEmail // Fallback to constant
		}
		if adminPassword == "" {
			adminPassword = "admin1234"
		}
		os.Args = []string{"pocketbase", "superuser", "upsert", adminEmail, adminPassword}
		if err := pb.Start(); err != nil {
			log.Printf("PocketBase failed with error: %v", err)
		} else {
			log.Println("PocketBase superuser created successfully")
			pb.Logger().Info("PocketBase superuser created successfully")
		}

		// Start PocketBase server first to ensure DB and tables exist
		os.Args = []string{"pocketbase", "serve", "--dir", ".", "--http", "127.0.0.1:8091"}
		if err := pb.Start(); err != nil {
			log.Fatalf("PocketBase failed with error: %v", err)
			panic(err)
		} else {
			log.Println("PocketBase server started successfully")
		}
	}()

	// Give PocketBase a moment to start up
	time.Sleep(2 * time.Second)
}

// Standalone tool to seed the PocketBase database with inventory items
func main() {
	log.Println("Initializing PocketBase service...")

	// Get the PocketBase data directory from environment variable or use default
	pbDataDir := os.Getenv("POCKETBASE_DATA_DIR")
	if pbDataDir == "" {
		// Use the same fallback as in dev-local.sh
		pbDataDir = "backend/backend/pb_data"
		log.Printf("POCKETBASE_DATA_DIR not set, using default: %s", pbDataDir)
	}
	log.Printf("Using PocketBase data directory: %s", pbDataDir)

	// Ensure the directory exists
	if err := os.MkdirAll(pbDataDir, 0755); err != nil {
		log.Fatalf("Failed to create PocketBase data directory: %v", err)
	}

	// Create PocketBase instance with explicit data directory
	pb := pocketbase.NewWithConfig(
		pocketbase.Config{
			DefaultDataDir: pbDataDir,
		})

	// List all files in the directory
	files, err := os.ReadDir(pbDataDir)
	if err != nil {
		log.Printf("Error reading directory: %v", err)
	} else {
		log.Printf("Files in directory: %d", len(files))
		for _, file := range files {
			log.Printf("  - %s (IsDir: %v)", file.Name(), file.IsDir())
		}
	}
	Start(pb)

	log.Println("Attempting to create collections...")

	// Create application data directory if it doesn't exist
	pbDataDir = os.Getenv("POCKETBASE_DATA_DIR")
	if pbDataDir == "" {
		// Use the same fallback as in dev-local.sh
		pbDataDir = "backend/backend/pb_data"
		log.Printf("POCKETBASE_DATA_DIR not set, using default: %s", pbDataDir)
	}
	log.Printf("Using PocketBase data directory: %s", pbDataDir)

	// Initialize PocketBase service with explicit port configuration
	// Use a different port to avoid conflicts with already running instance
	if err := os.MkdirAll(pbDataDir, 0755); err != nil {
		log.Printf("Failed to create PocketBase data directory %s: %v", pbDataDir, err)
		panic(err)
	}

	// Now seed the data using the dedicated seeder instance
	log.Println("Seeding with predefined base data...")
	if err := SeedPocketBaseData(pb); err != nil {
		log.Fatalf("Failed to seed base data: %v", err)
	}

	// Seed the database with random data
	log.Println("Seeding with random data...")
	if err := seedRandomData(pb); err != nil {
		log.Fatalf("Failed to seed base data: %v", err)
	}

	log.Println("Database seeding completed successfully!")
}

func seedRandomData(pb *pocketbase.PocketBase) error {
	// Find the collections
	itemTypesCollection, err := pb.FindCollectionByNameOrId("item_types")
	if err != nil {
		return fmt.Errorf("error finding item_types collection: %w", err)
	}

	itemInstancesCollection, err := pb.FindCollectionByNameOrId("item_instances")
	if err != nil {
		return fmt.Errorf("error finding item_instances collection: %w", err)
	}

	// Generate random inventory items
	log.Println("Creating 100 inventory types with low quantities...")
	createRandomInventoryItems(pb, itemTypesCollection, itemInstancesCollection, 100)

	return nil
}

func createRandomInventoryItems(app core.App, typesCollection, instancesCollection *core.Collection, count int) {

	// Define various options for item properties
	categories := []string{
		"Furniture", "Tableware", "Lighting", "Storage", "Decor", "Electronics",
		"Textiles", "Kitchenware", "Bedding", "Bath", "Outdoor", "Office",
	}

	subcategories := map[string][]string{
		"Furniture":   {"Chairs", "Sofas", "Stools", "Benches", "Ottomans", "Recliners", "Bar Stools"},
		"Tableware":   {"Dining Tables", "Coffee Tables", "Side Tables", "Console Tables", "Desks", "Nightstands"},
		"Lighting":    {"Chandeliers", "Pendants", "Floor Lamps", "Table Lamps", "Wall Sconces", "Ceiling Lights"},
		"Storage":     {"Shelves", "Cabinets", "Dressers", "Wardrobes", "Bookcases", "Media Units"},
		"Decor":       {"Mirrors", "Artwork", "Vases", "Sculptures", "Candles", "Frames", "Plants"},
		"Electronics": {"TVs", "Speakers", "Projectors", "Smart Home", "Media Players", "Cameras"},
		"Textiles":    {"Rugs", "Curtains", "Pillows", "Throws", "Bedding", "Tablecloths"},
		"Kitchenware": {"Dinnerware", "Glassware", "Flatware", "Cookware", "Serving Pieces", "Barware"},
		"Bedding":     {"Beds", "Mattresses", "Headboards", "Nightstands", "Bedding Sets", "Dressers"},
		"Bath":        {"Vanities", "Mirrors", "Shower Curtains", "Bath Accessories", "Towels", "Storage"},
		"Outdoor":     {"Patio Furniture", "Planters", "Umbrellas", "Fire Pits", "Hammocks", "Lighting"},
		"Office":      {"Desks", "Office Chairs", "Filing Cabinets", "Bookcases", "Desk Accessories", "Whiteboards"},
	}

	styles := []string{"Modern", "Contemporary", "Traditional", "Transitional", "Industrial", "Mid-Century",
		"Rustic", "Scandinavian", "Farmhouse", "Art Deco", "Bohemian", "Coastal", "Minimalist"}

	colors := []string{"Black", "White", "Gray", "Brown", "Beige", "Navy", "Green", "Red", "Blue", "Yellow",
		"Orange", "Purple", "Pink", "Teal", "Gold", "Silver", "Bronze", "Multi", "Natural", "Cream"}

	brands := []string{"DesignCo", "LuxHome", "ModernLiving", "ClassicStyles", "UrbanChic", "NatureHome",
		"ElegantDesigns", "PremiumFurniture", "ArtisanCraft", "EcoFurnish", "TrendySpace",
		"HeritageBrand", "InnovateDecor", "QualityLiving", "StyleMakers", "ComfortZone"}

	recordIds := make([]string, 0, count)
	skus := make([]string, 0, count)
	quantities := make([]int, 0, count)

	for i := 0; i < count; i++ {
		// Select random properties
		category := categories[rand.Intn(len(categories))]
		subcategoryOptions := subcategories[category]
		subcategory := subcategoryOptions[rand.Intn(len(subcategoryOptions))]
		style := styles[rand.Intn(len(styles))]
		color := colors[rand.Intn(len(colors))]
		brand := brands[rand.Intn(len(brands))]

		// Generate unique SKU
		skuPrefix := ""
		if len(category) >= 3 {
			skuPrefix = category[:3]
		} else {
			skuPrefix = category
		}
		if len(subcategory) >= 3 {
			skuPrefix += "-" + subcategory[:3]
		} else {
			skuPrefix += "-" + subcategory
		}
		sku := fmt.Sprintf("%s-%s-%03d", skuPrefix, strings.ToUpper(color[:3]), i+1)

		// Generate random low quantity (1-10)
		quantity := rand.Intn(10) + 1

		// Generate random price between $20-$2000
		price := 20.0 + float64(rand.Intn(198000))/100.0
		replacementValue := price * (1.2 + rand.Float64()*0.3) // 20-50% more than purchase price

		// Generate dimensions based on category
		var dimensions string
		switch category {
		case "Furniture":
			dimensions = fmt.Sprintf("%d\"W x %d\"D x %d\"H", 15+rand.Intn(30), 15+rand.Intn(25), 25+rand.Intn(20))
		case "Tableware":
			dimensions = fmt.Sprintf("%d\"W x %d\"D x %d\"H", 20+rand.Intn(60), 20+rand.Intn(40), 15+rand.Intn(20))
		case "Lighting":
			dimensions = fmt.Sprintf("%d\" diameter x %d\"H", 10+rand.Intn(30), 15+rand.Intn(40))
		case "Storage":
			dimensions = fmt.Sprintf("%d\"W x %d\"D x %d\"H", 20+rand.Intn(40), 15+rand.Intn(20), 30+rand.Intn(50))
		default:
			dimensions = fmt.Sprintf("%d\"W x %d\"D x %d\"H", 10+rand.Intn(30), 10+rand.Intn(30), 10+rand.Intn(30))
		}

		// Create name by combining properties
		name := fmt.Sprintf("%s %s %s", style, color, subcategory[:len(subcategory)-1]) // Remove 's' at the end
		if rand.Intn(2) == 0 {                                                          // 50% chance to add brand to name
			name = brand + " " + name
		}

		// Create description
		descriptions := []string{
			fmt.Sprintf("Elegant %s %s with %s finish", style, subcategory[:len(subcategory)-1], color),
			fmt.Sprintf("High-quality %s perfect for modern interiors", subcategory[:len(subcategory)-1]),
			fmt.Sprintf("%s-inspired %s with unique details", style, subcategory[:len(subcategory)-1]),
			fmt.Sprintf("Premium %s %s from %s collection", color, subcategory[:len(subcategory)-1], brand),
			fmt.Sprintf("Stylish %s with exceptional craftsmanship", subcategory[:len(subcategory)-1]),
			fmt.Sprintf("Versatile %s %s for any space", style, subcategory[:len(subcategory)-1]),
			fmt.Sprintf("Handcrafted %s with %s accents", subcategory[:len(subcategory)-1], color),
		}
		description := descriptions[rand.Intn(len(descriptions))]

		// Create notes
		notes := []string{
			"Limited stock item",
			"Special order only",
			"Popular rental item",
			"Handle with care",
			"Assembly required",
			"Discontinued model",
			"New arrival",
			"Customer favorite",
			"Seasonal item",
			"Premium collection",
		}
		note := notes[rand.Intn(len(notes))]

		// Create the item entry
		data := map[string]any{
			"name":                   name,
			"description":            description,
			"category":               category,
			"subcategory":            subcategory,
			"style":                  style,
			"color":                  color,
			"brand":                  brand,
			"dimensions":             dimensions,
			"purchase_unit_price":    price,
			"replacement_unit_value": replacementValue,
			"sku":                    sku,
			"total_quantity":         quantity,
			"notes":                  note,
			"images":                 []string{}, // Empty array for required images field
		}

		// Create the record
		record := core.NewRecord(typesCollection)
		record.Load(data)

		if err := app.Save(record); err != nil {
			log.Fatalf("Error saving item type %s: %v", name, err)
		}

		log.Printf("Created item type [%d/%d]: %s (SKU: %s, Qty: %d)", i+1, count, name, sku, quantity)

		// Store the ID for creating instances
		recordIds = append(recordIds, record.Id)
		skus = append(skus, sku)
		quantities = append(quantities, quantity)
	}

	// Create instances for each item type
	log.Println("Creating instances for each item type...")

	for i, id := range recordIds {
		sku := skus[i]
		quantity := quantities[i]

		log.Printf("Creating %d instances for item type %s (SKU: %s)", quantity, id, sku)

		for j := 1; j <= quantity; j++ {
			uniqueID := fmt.Sprintf("%s-%03d", sku, j)

			instanceData := map[string]any{
				"item_type":         id,
				"condition":         "New",
				"purchase_date":     "2023-01-01", // Default purchase date
				"unique_identifier": uniqueID,
				"notes":             "Automatically generated during seeding",
			}

			instanceRecord := core.NewRecord(instancesCollection)
			instanceRecord.Load(instanceData)

			if err := app.Save(instanceRecord); err != nil {
				log.Fatalf("Error saving item instance %s: %v", uniqueID, err)
			}
		}
	}
}

// SeedPocketBaseData seeds PocketBase collections if they are empty.
func SeedPocketBaseData(app core.App) error {
	// --- Seed item_types ---
	count, err := app.CountRecords("item_types", nil)
	if err != nil {
		return err
	}
	if count == 0 {
		log.Println("Seeding item_types...")
		collection, err := app.FindCollectionByNameOrId("item_types")
		if err != nil {
			return err
		}
		items := []map[string]any{
			{
				"name":                   "Modern Dining Chair",
				"description":            "Contemporary dining chair with metal legs",
				"category":               "Furniture",
				"subcategory":            "Dining Chairs",
				"style":                  "Modern",
				"color":                  "Black",
				"brand":                  "DesignCo",
				"dimensions":             "18\"W x 20\"D x 32\"H",
				"purchase_unit_price":    150.0,
				"replacement_unit_value": 200.0,
				"sku":                    "CHAIR-MDN-BLK",
				"total_quantity":         50,
				"notes":                  "Primary dining chair stock",
				"images":                 []string{}, // Empty array for required images field
			},
			{
				"name":                   "Velvet Sofa",
				"description":            "Luxurious velvet sofa with wooden legs",
				"category":               "Furniture",
				"subcategory":            "Sofas",
				"style":                  "Contemporary",
				"color":                  "Navy Blue",
				"brand":                  "LuxHome",
				"dimensions":             "84\"W x 36\"D x 32\"H",
				"purchase_unit_price":    1200.0,
				"replacement_unit_value": 1500.0,
				"sku":                    "SOFA-VLT-NVY",
				"total_quantity":         12,
				"notes":                  "Display models + warehouse stock",
				"images":                 []string{}, // Empty array for required images field
			},
			{
				"name":                   "Glass Coffee Table",
				"description":            "Round glass coffee table with brass base",
				"category":               "Furniture",
				"subcategory":            "Coffee Tables",
				"style":                  "Modern",
				"color":                  "Clear/Brass",
				"brand":                  "GlassCraft",
				"dimensions":             "36\" diameter x 18\"H",
				"purchase_unit_price":    400.0,
				"replacement_unit_value": 500.0,
				"sku":                    "TABLE-COF-GLS",
				"total_quantity":         25,
				"notes":                  "Fragile - handle with care",
				"images":                 []string{}, // Empty array for required images field
			},
			{
				"name":                   "Adjustable Desk Lamp",
				"description":            "LED desk lamp with adjustable arm",
				"category":               "Lighting",
				"subcategory":            "Table Lamps",
				"style":                  "Modern",
				"color":                  "Silver",
				"brand":                  "LightWorks",
				"dimensions":             "12\" base, 24\" max height",
				"purchase_unit_price":    80.0,
				"replacement_unit_value": 100.0,
				"sku":                    "LAMP-DSK-SLV",
				"total_quantity":         40,
				"notes":                  "Popular office lighting solution",
				"images":                 []string{}, // Empty array for required images field
			},
		}
		for _, data := range items {
			record := core.NewRecord(collection)
			record.Load(data)
			if err := app.Save(record); err != nil {
				return err
			}
		}
	}

	// --- Seed clients ---
	count, err = app.CountRecords("clients", nil)
	if err != nil {
		return err
	}
	if count == 0 {
		log.Println("Seeding clients...")
		collection, err := app.FindCollectionByNameOrId("clients")
		if err != nil {
			return err
		}
		clients := []map[string]any{
			{
				"name":           "Luxury Apartments Inc.",
				"contact_person": "Robert Williams",
				"phone":          "555-3456",
				"email":          "<EMAIL>",
				"address":        "789 Luxury Blvd",
				"city":           "New York",
				"state":          "NY",
				"zip":            "10003",
				"client_since":   "2023-05-13",
				"notes":          "High-end residential client",
				"client_type":    "Home Owner",
			},
			{
				"name":           "Modern Office Spaces",
				"contact_person": "Jennifer Lee",
				"phone":          "555-7890",
				"email":          "<EMAIL>",
				"address":        "101 Business Park",
				"city":           "New York",
				"state":          "NY",
				"zip":            "10004",
				"client_since":   "2022-11-13",
				"notes":          "Commercial office client",
				"client_type":    "Realtor",
			},
			{
				"name":           "Urban Design Collective",
				"contact_person": "Maria Rodriguez",
				"phone":          "555-1122",
				"email":          "<EMAIL>",
				"address":        "456 Creative Ave",
				"city":           "Brooklyn",
				"state":          "NY",
				"zip":            "11201",
				"client_since":   "2023-01-20",
				"notes":          "Focus on modern and sustainable design.",
				"client_type":    "Realtor",
			},
			{
				"name":           "HomeStagers Pro",
				"contact_person": "David Chen",
				"phone":          "555-8877",
				"email":          "<EMAIL>",
				"address":        "789 Staging Ln",
				"city":           "Queens",
				"state":          "NY",
				"zip":            "11354",
				"client_since":   "2021-07-01",
				"notes":          "Specializes in residential staging.",
				"client_type":    "Home Owner",
			},
		}
		for _, data := range clients {
			record := core.NewRecord(collection)
			record.Load(data)
			if err := app.Save(record); err != nil {
				return err
			}
		}
	}

	// --- Seed item_instances ---
	// Check if there are any item instances already
	instanceCount, err := app.CountRecords("item_instances", nil)
	if err != nil {
		return err
	}

	if instanceCount == 0 {
		log.Println("Seeding item_instances...")
		itemTypesCollection, err := app.FindCollectionByNameOrId("item_types")
		if err != nil {
			return err
		}

		// Get all item types
		records, err := app.FindAllRecords(itemTypesCollection.Id, nil)
		if err != nil {
			return err
		}

		// Create item instances for each item type
		for _, itemTypeRecord := range records {
			sku := itemTypeRecord.GetString("sku")
			totalQty := itemTypeRecord.GetInt("total_quantity")

			if totalQty > 0 {
				log.Printf("Creating %d instances for %s", totalQty, sku)

				// Get item_instances collection
				instancesCollection, err := app.FindCollectionByNameOrId("item_instances")
				if err != nil {
					return err
				}

				for i := 1; i <= totalQty; i++ {
					instanceData := map[string]any{
						"item_type":         itemTypeRecord.Id,
						"condition":         "New",
						"purchase_date":     "2023-01-01", // Default purchase date
						"unique_identifier": fmt.Sprintf("%s-%03d", sku, i),
						"notes":             "Automatically generated during seeding",
					}

					record := core.NewRecord(instancesCollection)
					record.Load(instanceData)
					if err := app.Save(record); err != nil {
						return err
					}
				}
			}
		}
	}

	// --- Seed locations ---
	count, err = app.CountRecords("locations", nil)
	if err != nil {
		log.Printf("Error counting locations: %v", err)
		return err
	}
	log.Printf("Found %d existing locations", count)

	if count == 0 {
		log.Println("Seeding locations...")
		collection, err := app.FindCollectionByNameOrId("locations")
		if err != nil {
			log.Printf("Error finding locations collection: %v", err)
			return err
		}

		locations := []map[string]any{
			{
				"name":          "Main Warehouse",
				"address":       "123 Storage Ave",
				"city":          "New York",
				"state":         "NY",
				"zip":           "10001",
				"contact_name":  "John Smith",
				"contact_phone": "555-1234",
				"contact_email": "<EMAIL>",
				"location_type": "warehouse",
				"notes":         "Primary storage location",
			},
			{
				"name":          "Downtown Showroom",
				"address":       "456 Display St",
				"city":          "New York",
				"state":         "NY",
				"zip":           "10002",
				"contact_name":  "Sarah Johnson",
				"contact_phone": "555-5678",
				"contact_email": "<EMAIL>",
				"location_type": "showroom",
				"notes":         "Client-facing display area",
			},
			{
				"name":          "Brooklyn Storage Unit",
				"address":       "777 Storage Dr",
				"city":          "Brooklyn",
				"state":         "NY",
				"zip":           "11222",
				"contact_name":  "Mike Brown",
				"contact_phone": "555-3344",
				"contact_email": "<EMAIL>",
				"location_type": "storage",
				"notes":         "Secondary storage for overflow.",
			},
			{
				"name":          "Client Project Site A",
				"address":       "100 Project Road",
				"city":          "Manhattan",
				"state":         "NY",
				"zip":           "10010",
				"contact_name":  "On-Site Manager",
				"contact_phone": "555-9900",
				"contact_email": "<EMAIL>",
				"location_type": "client_site",
				"notes":         "Temporary staging location for Project Alpha",
			},
		}

		for _, data := range locations {
			record := core.NewRecord(collection)
			record.Load(data)
			if err := app.Save(record); err != nil {
				log.Printf("Error saving location: %v", err)
				return err
			}
		}
		log.Printf("Created %d locations", len(locations))
	}

	// --- Seed projects ---
	count, err = app.CountRecords("projects", nil)
	if err != nil {
		log.Printf("Error counting projects: %v", err)
		return err
	}
	log.Printf("Found %d existing projects", count)

	if count == 0 {
		log.Println("Seeding projects...")
		collection, err := app.FindCollectionByNameOrId("projects")
		if err != nil {
			log.Printf("Error finding projects collection: %v", err)
			return err
		}

		// Ensure at least one client exists
		clientRecords, err := app.FindAllRecords("clients", nil)
		if err != nil {
			log.Printf("Error finding clients: %v", err)
		}

		log.Printf("Found %d clients", len(clientRecords))

		if len(clientRecords) == 0 {
			collection, err := app.FindCollectionByNameOrId("clients")
			if err != nil {
				return fmt.Errorf("clients collection not found: %w", err)
			}
			data := map[string]any{
				"name":           "Default Client",
				"contact_person": "Default Contact",
				"phone":          "555-0000",
				"email":          "<EMAIL>",
				"address":        "123 Default St",
				"city":           "Default City",
				"state":          "DC",
				"zip":            "00000",
				"client_since":   "2024-01-01",
				"notes":          "Auto-generated default client",
			}
			record := core.NewRecord(collection)
			record.Load(data)
			if err := app.Save(record); err != nil {
				return fmt.Errorf("failed to seed default client: %w", err)
			}
			clientRecords, _ = app.FindAllRecords("clients", nil)
			if len(clientRecords) == 0 {
				return fmt.Errorf("no clients found after seeding default client")
			}
		}
		clientId := clientRecords[0].Id
		log.Printf("Using client ID: %s", clientId)

		// Ensure at least one location exists
		locationRecords, err := app.FindAllRecords("locations", nil)
		if err != nil {
			log.Printf("Error finding locations: %v", err)
			// It's critical to return here if we can't find locations,
			// as project seeding depends on it.
			return fmt.Errorf("error finding locations for project seeding: %w", err)
		}

		log.Printf("Found %d locations for project seeding", len(locationRecords))

		if len(locationRecords) == 0 {
			collection, err := app.FindCollectionByNameOrId("locations")
			if err != nil {
				return fmt.Errorf("locations collection not found: %w", err)
			}
			data := map[string]any{
				"name":    "Default Location",
				"address": "100 Main St",
				"city":    "Default City",
				"state":   "DC",
				"zip":     "00000",
				"notes":   "Auto-generated default location",
			}
			record := core.NewRecord(collection)
			record.Load(data)
			if err := app.Save(record); err != nil {
				return fmt.Errorf("failed to seed default location: %w", err)
			}
			locationRecords, _ = app.FindAllRecords("locations", nil)
			if len(locationRecords) == 0 {
				return fmt.Errorf("no locations found after seeding default location")
			}
		}
		locationId := locationRecords[0].Id
		log.Printf("Using location ID: %s", locationId)

		projects := []map[string]any{
			{
				"name":       "Downtown Renovation",
				"client":     clientId,
				"location":   locationId,
				"start_date": "2024-05-01",
				"end_date":   "2024-08-15",
				"status":     "active",
				"notes":      "Major renovation of lobby and common areas.",
			},
			{
				"name":       "Office Expansion",
				"client":     clientId,
				"location":   locationId,
				"start_date": "2024-06-10",
				"end_date":   "2024-09-30",
				"status":     "planned",
				"notes":      "Expansion to 3rd floor, pending permits.",
			},
			{
				"name":       "Brooklyn Loft Staging",
				"client":     clientRecords[1%len(clientRecords)].Id,     // Use another client if available
				"location":   locationRecords[1%len(locationRecords)].Id, // Use another location
				"start_date": "2024-07-01",
				"end_date":   "2024-07-15",
				"status":     "active",
				"notes":      "Full staging for a luxury loft.",
			},
			{
				"name":       "Residential Remodel - Queens",
				"client":     clientRecords[2%len(clientRecords)].Id,     // Use another client
				"location":   locationRecords[2%len(locationRecords)].Id, // Use another location
				"start_date": "2024-08-01",
				"end_date":   "2024-12-31",
				"status":     "planned",
				"notes":      "Complete home remodel.",
			},
		}
		for _, data := range projects {
			record := core.NewRecord(collection)
			record.Load(data)
			if err := app.Save(record); err != nil {
				// Provide more context in error logging
				log.Printf("Error saving project '%s': %v. Data: %+v", data["name"], err, data)
				return err
			}
		}
		log.Printf("Finished creating %d projects", len(projects))
	}

	// --- Seed project_inventories (linking item_types to projects) ---
	log.Println("Attempting to seed project_inventories...")
	projectInventoryCount, err := app.CountRecords("project_inventories", nil)
	if err != nil {
		log.Printf("Error counting project_inventories: %v", err)
		// Log and proceed, assuming schema creation handles it or it's an acceptable first-run state.
	}

	if projectInventoryCount == 0 {
		log.Println("Seeding project_inventories...")

		allProjects, err := app.FindAllRecords("projects", nil)
		if err != nil || len(allProjects) == 0 {
			log.Printf("Error finding projects or no projects exist to link item types to: %v", err)
			return fmt.Errorf("no projects found to seed project_inventories: %w", err)
		}
		log.Printf("Found %d projects to link item types to.", len(allProjects))

		allItemTypes, err := app.FindAllRecords("item_types", nil)
		if err != nil || len(allItemTypes) == 0 {
			log.Printf("Error finding item_types or no item_types exist to link: %v", err)
			return fmt.Errorf("no item_types found to seed project_inventories: %w", err)
		}
		log.Printf("Found %d item types to potentially link.", len(allItemTypes))

		projectInventoryCollection, err := app.FindCollectionByNameOrId("project_inventories")
		if err != nil {
			log.Printf("Error finding project_inventories collection. Ensure it's created in your schema: %v", err)
			return fmt.Errorf("project_inventories collection not found: %w. Please ensure it's defined in schema.json and migrations are run.", err)
		}

		// Seed about 10-20 project_inventory records
		numItemsToSeed := 0
		if len(allItemTypes) > 0 && len(allProjects) > 0 {
			numItemsToSeed = rand.Intn(11) + 10 // 10 to 20 records
			if numItemsToSeed > len(allItemTypes) {
				numItemsToSeed = len(allItemTypes)
			}
		}
		log.Printf("Attempting to seed %d project_inventories records.", numItemsToSeed)

		seededItemTypeToProject := make(map[string]bool) // To avoid duplicate (item_type, project) pairs in this seed run

		for i := 0; i < numItemsToSeed; i++ {
			// Select a random item_type
			itemType := allItemTypes[rand.Intn(len(allItemTypes))]

			// Select a random project
			project := allProjects[rand.Intn(len(allProjects))]

			// Ensure we don't seed the same item_type to the same project multiple times in this run
			pairKey := fmt.Sprintf("%s-%s", itemType.Id, project.Id)
			if seededItemTypeToProject[pairKey] {
				log.Printf("Skipping duplicate seed attempt for item_type %s to project %s", itemType.Id, project.Id)
				// Try to find a different combination or reduce i to try again (optional, for simplicity just skip)
				// For this simple seed, we can just decrement i and continue to try to meet numItemsToSeed
				// but this could lead to an infinite loop if all combinations are exhausted and numItemsToSeed is high.
				// A better approach for robust seeding would be to pre-generate unique pairs.
				// For now, let's just allow potential under-seeding if collisions are frequent.
				continue
			}

			// Assign a random quantity, ensuring it's not more than item_type.total_quantity
			maxQty := itemType.GetInt("total_quantity")
			if maxQty <= 0 {
				log.Printf("Skipping item_type %s (%s) for project %s as its total_quantity is %d.", itemType.Id, itemType.GetString("name"), project.Id, maxQty)
				continue // Skip if item type has no quantity
			}

			quantityToAssign := 1
			if maxQty > 1 {
				quantityToAssign = rand.Intn(maxQty-1) + 1 // Assign between 1 and maxQty
			}

			assignedDate := time.Now().AddDate(0, -rand.Intn(2), -rand.Intn(28)).Format("2006-01-02") // Random date in last 2 months
			notes := fmt.Sprintf("Seeded %d units of %s for project %s", quantityToAssign, itemType.GetString("name"), project.GetString("name"))
			dailyRate := (itemType.GetFloat("replacement_unit_value") * 0.01) + float64(rand.Intn(5)) // Example daily rate: 1-6% of replacement value

			projectInventoryData := map[string]any{
				"project":       project.Id,
				"item_type":     itemType.Id,
				"quantity":      quantityToAssign,
				"assigned_date": assignedDate,
				"daily_rate":    dailyRate,
				// "removed_date": nil, // Assuming not removed initially
				"notes": notes,
			}

			record := core.NewRecord(projectInventoryCollection)
			record.Load(projectInventoryData)
			if err := app.Save(record); err != nil {
				log.Printf("Error saving project_inventories for item_type %s to project %s: %v. Data: %+v", itemType.Id, project.Id, err, projectInventoryData)
				// Continue to try seeding other items
			} else {
				log.Printf("Successfully seeded project_inventories: %d of item_type %s to project %s", quantityToAssign, itemType.Id, project.Id)
				seededItemTypeToProject[pairKey] = true
			}
		}
		log.Printf("Finished seeding project_inventories. Attempted to seed %d records.", numItemsToSeed)

	} else {
		log.Printf("Skipping project_inventories seeding as %d records already exist.", projectInventoryCount)
	}

	return nil
}
