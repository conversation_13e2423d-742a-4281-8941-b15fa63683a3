// main.go
package main

import (
	"log"
	"os"
	"path/filepath"

	"stagewise-backend/internal"

	"github.com/adrg/xdg"
	"github.com/pocketbase/pocketbase"
	"github.com/pocketbase/pocketbase/plugins/migratecmd"
	// enable once you have at least one migration
	// _ "yourpackage/migrations"
)

// Standalone tool to seed the PocketBase database with inventory items
func main() {
	log.Println("Initializing PocketBase service...")

	// Get the PocketBase data directory
	appDataDir := filepath.Join(xdg.DataHome, internal.AppNameLower)
	pbDataDir := filepath.Join(appDataDir, "pb_data")
	log.Printf("Using PocketBase data directory: %s", pbDataDir)

	// Initialize PocketBase service with explicit port configuration
	// Use a different port to avoid conflicts with already running instance
	if err := os.MkdirAll(pbDataDir, 0755); err != nil {
		log.Printf("Failed to create PocketBase data directory %s: %v", pbDataDir, err)
		panic(err)
	}

	// Create PocketBase instance with explicit data directory
	pb := pocketbase.NewWithConfig(
		pocketbase.Config{
			DefaultDataDir: pbDataDir,
		})

	migratecmd.MustRegister(pb, pb.RootCmd, migratecmd.Config{
		// enable auto creation of migration files when making collection changes in the Dashboard
		// (the isGoRun check is to enable it only during development)
		Automigrate: true,
	})

	// Bootstrap the PocketBase application
	if err := pb.Bootstrap(); err != nil {
		log.Fatalf("Failed to bootstrap PocketBase: %v", err)
	}

	// Run . migrate collections
	pb.RootCmd.SetArgs([]string{"migrate", "collections"})
	if err := pb.RootCmd.Execute(); err != nil {
		log.Fatalf("Failed to execute command 'migrate collections': %v", err)
	}
}
