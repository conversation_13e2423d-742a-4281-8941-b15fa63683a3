package internal

import (
	"fmt"

	"github.com/pocketbase/pocketbase/core"
	"github.com/pocketbase/pocketbase/tools/types"
)

// CreateClientsCollection creates the 'clients' collection if it does not exist.
func CreateClientsCollection(app core.App) error {

	collection := core.NewBaseCollection("clients")

	collection.Fields.Add(&core.EmailField{
		Name:     "email",
		Required: false,
	})
	collection.Fields.Add(&core.SelectField{
		Name:      "client_type",
		Required:  true,
		MaxSelect: 1,
		Values:    []string{"Realtor", "Home Owner", "Other"},
	})
	collection.Fields.Add(&core.TextField{
		Name:     "primary_phone",
		Required: false,
		Max:      40,
		Min:      0,
		Pattern:  `^[\d() -]+$`,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "secondary_phone",
		Required: false,
		Max:      40,
		Min:      0,
		Pattern:  `^[\d() -]+$`,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "name",
		Required: true,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "notes",
		Required: false,
		Max:      0,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "address",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "state",
		Required: false,
		Max:      100,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "contact_person",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "city",
		Required: false,
		Max:      100,
		Min:      0,
	})

	collection.AddIndex("idx_clients_name", false, "name", "")
	collection.ListRule = types.Pointer("")
	collection.ViewRule = types.Pointer("")

	collection.CreateRule = types.Pointer("")
	collection.UpdateRule = types.Pointer("")
	collection.DeleteRule = types.Pointer("")

	// It's good practice to check if the collection already exists before trying to save.
	// However, app.Save(collection) handles this by updating if it exists or creating if it doesn't.
	// For views, it might re-apply the view query.
	// If a more explicit "create only if not exists" is needed, one would app.Dao().FindCollectionByNameOrId first.
	return app.Save(collection)
}

// CreateItemTypesCollection creates the 'item_types' collection if it does not exist.
func CreateItemTypesCollection(app core.App) error {

	collection := core.NewBaseCollection("item_types")

	collection.Fields.Add(&core.AutodateField{
		Name:     "created_on",
		OnCreate: true,
	})
	collection.Fields.Add(&core.AutodateField{
		Name:     "updated_on",
		OnCreate: true,
		OnUpdate: true,
	})
	collection.Fields.Add(&core.FileField{
		Name:      "images",
		Required:  false,
		MaxSelect: 12,
		MaxSize:   0,
		MimeTypes: []string{"image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"},
		Thumbs:    []string{"400x600", "400x0", "0x600"},
	})
	collection.Fields.Add(&core.NumberField{
		Name:     "purchase_unit_price",
		Required: false,
		Min:      types.Pointer(0.0),
	})
	collection.Fields.Add(&core.NumberField{
		Name:     "replacement_unit_value",
		Required: true,
		Min:      types.Pointer(0.0),
	})
	collection.Fields.Add(&core.NumberField{
		Name:     "total_quantity",
		Required: true,
		Min:      types.Pointer(0.0),
	})
	collection.Fields.Add(&core.SelectField{
		Name:      "category",
		Required:  true,
		MaxSelect: 0,
		Values:    []string{"Furniture", "Artwork", "Lighting", "Rugs", "Decor", "Plants", "Mirrors", "Bedding", "Kitchenware", "Electronics", "Outdoor", "Seating", "Bedding", "Storage", "Accessories", "Wall Art", "Tableware", "Textiles", "Books", "Bath", "Office", "Miscellaneous"},
	})
	collection.Fields.Add(&core.TextField{
		Name:     "name",
		Required: true,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "color",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "description",
		Required: false,
		Max:      1000,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "notes",
		Required: false,
		Max:      2000,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "subcategory",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "barcode",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "sku",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "dimensions",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "brand",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "style",
		Required: false,
		Max:      255,
		Min:      0,
	})

	collection.ListRule = types.Pointer("")
	collection.ViewRule = types.Pointer("")

	collection.CreateRule = types.Pointer("")
	collection.UpdateRule = types.Pointer("")
	collection.DeleteRule = types.Pointer("")

	// It's good practice to check if the collection already exists before trying to save.
	// However, app.Save(collection) handles this by updating if it exists or creating if it doesn't.
	// For views, it might re-apply the view query.
	// If a more explicit "create only if not exists" is needed, one would app.Dao().FindCollectionByNameOrId first.
	return app.Save(collection)
}

// CreateLocationsCollection creates the 'locations' collection if it does not exist.
func CreateLocationsCollection(app core.App) error {

	collection := core.NewBaseCollection("locations")

	collection.Fields.Add(&core.BoolField{
		Name:     "default",
		Required: false,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "zip",
		Required: false,
		Max:      20,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "name",
		Required: true,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "contact_phone",
		Required: false,
		Max:      100,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "notes",
		Required: false,
		Max:      0,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "contact_name",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "location_type",
		Required: true,
		Max:      100,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "address",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "state",
		Required: false,
		Max:      100,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "contact_email",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "city",
		Required: false,
		Max:      100,
		Min:      0,
	})

	collection.AddIndex("idx_locations_name", false, "name", "")
	collection.ListRule = types.Pointer("")
	collection.ViewRule = types.Pointer("")

	collection.CreateRule = types.Pointer("")
	collection.UpdateRule = types.Pointer("")
	collection.DeleteRule = types.Pointer("")

	// It's good practice to check if the collection already exists before trying to save.
	// However, app.Save(collection) handles this by updating if it exists or creating if it doesn't.
	// For views, it might re-apply the view query.
	// If a more explicit "create only if not exists" is needed, one would app.Dao().FindCollectionByNameOrId first.
	return app.Save(collection)
}

// CreateInventoryLocationsCollection creates the 'inventory_locations' collection if it does not exist.
func CreateInventoryLocationsCollection(app core.App) error {

	collection := core.NewBaseCollection("inventory_locations")

	itemtypesCollection, err := app.FindCollectionByNameOrId("item_types")
	if err != nil {
		return fmt.Errorf("dependency collection 'item_types' not found for 'inventory_locations': %w", err)
	}
	locationsCollection, err := app.FindCollectionByNameOrId("locations")
	if err != nil {
		return fmt.Errorf("dependency collection 'locations' not found for 'inventory_locations': %w", err)
	}

	collection.Fields.Add(&core.NumberField{
		Name:     "quantity",
		Required: true,
		Min:      types.Pointer(0.0),
	})
	collection.Fields.Add(&core.RelationField{
		Name:          "item_type",
		Required:      true,
		CollectionId:  itemtypesCollection.Id,
		MaxSelect:     1,
		CascadeDelete: false,
	})
	collection.Fields.Add(&core.RelationField{
		Name:          "location",
		Required:      true,
		CollectionId:  locationsCollection.Id,
		MaxSelect:     1,
		CascadeDelete: false,
	})
	collection.Fields.Add(&core.SelectField{
		Name:      "status",
		Required:  true,
		MaxSelect: 0,
		Values:    []string{"in_place", "pending_pickup", "in_transit"},
	})
	collection.Fields.Add(&core.TextField{
		Name:     "unique_location",
		Required: true,
		Max:      255,
		Min:      0,
	})

	collection.AddIndex("idx_inventory_item_type", false, "item_type", "")
	collection.AddIndex("idx_inventory_location", false, "location", "")
	collection.ListRule = types.Pointer("")
	collection.ViewRule = types.Pointer("")

	collection.CreateRule = types.Pointer("")
	collection.UpdateRule = types.Pointer("")
	collection.DeleteRule = types.Pointer("")

	// It's good practice to check if the collection already exists before trying to save.
	// However, app.Save(collection) handles this by updating if it exists or creating if it doesn't.
	// For views, it might re-apply the view query.
	// If a more explicit "create only if not exists" is needed, one would app.Dao().FindCollectionByNameOrId first.
	return app.Save(collection)
}

// CreateItemInstancesCollection creates the 'item_instances' collection if it does not exist.
func CreateItemInstancesCollection(app core.App) error {

	collection := core.NewBaseCollection("item_instances")

	itemtypesCollection, err := app.FindCollectionByNameOrId("item_types")
	if err != nil {
		return fmt.Errorf("dependency collection 'item_types' not found for 'item_instances': %w", err)
	}

	collection.Fields.Add(&core.AutodateField{
		Name:     "created_on",
		OnCreate: true,
	})
	collection.Fields.Add(&core.AutodateField{
		Name:     "updated_on",
		OnCreate: true,
		OnUpdate: true,
	})
	collection.Fields.Add(&core.BoolField{
		Name:     "in_service",
		Required: false,
	})
	collection.Fields.Add(&core.DateField{
		Name:     "purchase_date",
		Required: false,
	})
	collection.Fields.Add(&core.NumberField{
		Name:     "purchase_price",
		Required: false,
	})
	collection.Fields.Add(&core.RelationField{
		Name:          "item_type",
		Required:      true,
		CollectionId:  itemtypesCollection.Id,
		MaxSelect:     1,
		CascadeDelete: false,
	})
	collection.Fields.Add(&core.SelectField{
		Name:      "condition",
		Required:  false,
		MaxSelect: 1,
		Values:    []string{"New", "Excellent", "Good", "Fair", "Damaged", "Unserviceable"},
	})
	collection.Fields.Add(&core.TextField{
		Name:     "unique_identifier",
		Required: false,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "notes",
		Required: false,
		Max:      2000,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "serial_number",
		Required: false,
		Max:      255,
		Min:      0,
	})

	collection.AddIndex("idx_instances_item_type", false, "item_type", "")
	collection.AddIndex("idx_instances_unique_id", false, "unique_identifier", "")
	collection.ListRule = types.Pointer("")
	collection.ViewRule = types.Pointer("")

	collection.CreateRule = types.Pointer("")
	collection.UpdateRule = types.Pointer("")
	collection.DeleteRule = types.Pointer("")

	// It's good practice to check if the collection already exists before trying to save.
	// However, app.Save(collection) handles this by updating if it exists or creating if it doesn't.
	// For views, it might re-apply the view query.
	// If a more explicit "create only if not exists" is needed, one would app.Dao().FindCollectionByNameOrId first.
	return app.Save(collection)
}

// CreateProjectsCollection creates the 'projects' collection if it does not exist.
func CreateProjectsCollection(app core.App) error {

	collection := core.NewBaseCollection("projects")

	locationsCollection, err := app.FindCollectionByNameOrId("locations")
	if err != nil {
		return fmt.Errorf("dependency collection 'locations' not found for 'projects': %w", err)
	}
	clientsCollection, err := app.FindCollectionByNameOrId("clients")
	if err != nil {
		return fmt.Errorf("dependency collection 'clients' not found for 'projects': %w", err)
	}

	collection.Fields.Add(&core.DateField{
		Name:     "end_date",
		Required: false,
	})
	collection.Fields.Add(&core.DateField{
		Name:     "start_date",
		Required: true,
	})
	collection.Fields.Add(&core.NumberField{
		Name:     "project_value",
		Required: false,
		Min:      types.Pointer(0.0),
	})
	collection.Fields.Add(&core.RelationField{
		Name:          "location",
		Required:      true,
		CollectionId:  locationsCollection.Id,
		MaxSelect:     1,
		CascadeDelete: false,
	})
	collection.Fields.Add(&core.RelationField{
		Name:          "client",
		Required:      true,
		CollectionId:  clientsCollection.Id,
		MaxSelect:     1,
		CascadeDelete: false,
	})
	collection.Fields.Add(&core.SelectField{
		Name:      "status",
		Required:  true,
		MaxSelect: 0,
		Values:    []string{"planned", "active", "completed", "cancelled"},
	})
	collection.Fields.Add(&core.TextField{
		Name:     "name",
		Required: true,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "notes",
		Required: false,
		Max:      0,
		Min:      0,
	})

	collection.AddIndex("idx_projects_client", false, "client", "")
	collection.AddIndex("idx_projects_status", false, "status", "")
	collection.ListRule = types.Pointer("")
	collection.ViewRule = types.Pointer("")

	collection.CreateRule = types.Pointer("")
	collection.UpdateRule = types.Pointer("")
	collection.DeleteRule = types.Pointer("")

	// It's good practice to check if the collection already exists before trying to save.
	// However, app.Save(collection) handles this by updating if it exists or creating if it doesn't.
	// For views, it might re-apply the view query.
	// If a more explicit "create only if not exists" is needed, one would app.Dao().FindCollectionByNameOrId first.
	return app.Save(collection)
}

// CreateProjectInventoriesCollection creates the 'project_inventories' collection if it does not exist.
func CreateProjectInventoriesCollection(app core.App) error {

	collection := core.NewBaseCollection("project_inventories")

	itemtypesCollection, err := app.FindCollectionByNameOrId("item_types")
	if err != nil {
		return fmt.Errorf("dependency collection 'item_types' not found for 'project_inventories': %w", err)
	}
	projectsCollection, err := app.FindCollectionByNameOrId("projects")
	if err != nil {
		return fmt.Errorf("dependency collection 'projects' not found for 'project_inventories': %w", err)
	}

	collection.Fields.Add(&core.DateField{
		Name:     "assigned_date",
		Required: true,
	})
	collection.Fields.Add(&core.DateField{
		Name:     "removed_date",
		Required: false,
	})
	collection.Fields.Add(&core.NumberField{
		Name:     "daily_rate",
		Required: true,
		Min:      types.Pointer(0.0),
	})
	collection.Fields.Add(&core.NumberField{
		Name:     "quantity",
		Required: true,
		Min:      types.Pointer(1.0),
	})
	collection.Fields.Add(&core.RelationField{
		Name:          "item_type",
		Required:      true,
		CollectionId:  itemtypesCollection.Id,
		MaxSelect:     1,
		CascadeDelete: false,
	})
	collection.Fields.Add(&core.RelationField{
		Name:          "project",
		Required:      true,
		CollectionId:  projectsCollection.Id,
		MaxSelect:     1,
		CascadeDelete: true,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "notes",
		Required: false,
		Max:      0,
		Min:      0,
	})

	collection.AddIndex("idx_project_inventory_project", false, "project", "")
	collection.AddIndex("idx_project_inventory_item", false, "item_type", "")
	collection.ListRule = types.Pointer("")
	collection.ViewRule = types.Pointer("")

	collection.CreateRule = types.Pointer("")
	collection.UpdateRule = types.Pointer("")
	collection.DeleteRule = types.Pointer("")

	// It's good practice to check if the collection already exists before trying to save.
	// However, app.Save(collection) handles this by updating if it exists or creating if it doesn't.
	// For views, it might re-apply the view query.
	// If a more explicit "create only if not exists" is needed, one would app.Dao().FindCollectionByNameOrId first.
	return app.Save(collection)
}

// CreateViewActiveProjectsCollection creates the 'view_active_projects' view collection if it does not exist.
func CreateViewActiveProjectsCollection(app core.App) error {
	collection := core.NewViewCollection("view_active_projects")
	collection.ViewQuery = `
		SELECT 
			(ROW_NUMBER() OVER()) as id,
			p.id AS project_id,
			p.name AS project_name,
			c.name AS client_name,
			l.name AS location_name,
			p.start_date,
			p.end_date,
			it.name AS item_name,
			pi.quantity,
			pi.daily_rate
		FROM 
			projects p
		JOIN 
			clients c ON p.client = c.id
		JOIN 
			locations l ON p.location = l.id
		JOIN 
			project_inventories pi ON p.id = pi.project
		JOIN 
			item_types it ON pi.item_type = it.id
		WHERE 
			p.status = 'active'
	`

	projectsCollection, err := app.FindCollectionByNameOrId("projects")
	if err != nil {
		return fmt.Errorf("dependency collection 'projects' not found for 'view_active_projects': %w", err)
	}

	collection.Fields.Add(&core.DateField{
		Name:     "end_date",
		Required: false,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "item_name",
		Required: true,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.NumberField{
		Name:     "daily_rate",
		Required: true,
		Min:      types.Pointer(0.0),
	})
	collection.Fields.Add(&core.TextField{
		Name:     "project_name",
		Required: true,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "location_name",
		Required: true,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.NumberField{
		Name:     "quantity",
		Required: true,
		Min:      types.Pointer(1.0),
	})
	collection.Fields.Add(&core.TextField{
		Name:     "client_name",
		Required: true,
		Max:      255,
		Min:      0,
	})
	collection.Fields.Add(&core.DateField{
		Name:     "start_date",
		Required: true,
	})
	collection.Fields.Add(&core.RelationField{
		Name:          "project_id",
		Required:      false,
		CollectionId:  projectsCollection.Id,
		MaxSelect:     1,
		CascadeDelete: false,
	})
	collection.Fields.Add(&core.TextField{
		Name:     "id",
		Required: true,
		Max:      0,
		Min:      0,
		Pattern:  `^[a-z0-9]+$`,
	})

	collection.ListRule = types.Pointer("")
	collection.ViewRule = types.Pointer("")
	collection.CreateRule = nil
	collection.UpdateRule = nil
	collection.DeleteRule = nil

	// It's good practice to check if the collection already exists before trying to save.
	// However, app.Save(collection) handles this by updating if it exists or creating if it doesn't.
	// For views, it might re-apply the view query.
	// If a more explicit "create only if not exists" is needed, one would app.Dao().FindCollectionByNameOrId first.
	return app.Save(collection)
}

// CreateAllGeneratedCollections iterates over all generated collection creation functions and executes them.
// It's designed to be called once during application initialization.
func CreateAllGeneratedCollections(app core.App) error {
	var multiErr error
	collectionCreationOrder := []func(core.App) error{
		CreateClientsCollection,
		CreateItemTypesCollection,
		CreateLocationsCollection,
		CreateInventoryLocationsCollection,
		CreateItemInstancesCollection,
		CreateProjectsCollection,
		CreateProjectInventoriesCollection,
		CreateViewActiveProjectsCollection,
	}

	for _, createFunc := range collectionCreationOrder {
		// It's important to handle dependencies. The current generator sorts collections alphabetically,
		// which might not be the correct order for creation if there are dependencies (e.g., a 'relation' field).
		// The relation checks inside each function help, but a topological sort based on dependencies
		// for the order of calls here would be more robust if strict creation order is critical.
		// For now, we rely on the individual functions' relation checks and PocketBase's ability to handle some out-of-order saves.
		if err := createFunc(app); err != nil {
			// This will return on the first error. If you want to try all and collect errors,
			// you'd need a more complex error handling mechanism (e.g., using a list of errors).
			// For simplicity, we return on the first failure.
			// The error from createFunc should be descriptive enough.
			if multiErr == nil {
				multiErr = err // Capture the first error
			} else {
				// If you want to collect all errors:
				// multiErr = fmt.Errorf("%v; %w", multiErr, err)
			}
			// For now, let's log and continue, or return first error
			// log.Printf("Error creating collection: %v", err) // Requires "log" import
		}
	}
	return multiErr // Return the first error encountered, or nil if all succeed
}
